<script lang="ts">
    import {createEventDispatcher, onDestroy, onMount} from 'svelte';
    import BookListItem from '$lib/components/ui/BookListItem.svelte';
    import BookListItemCurrentlyReading from '$lib/components/ui/BookListItemCurrentlyReading.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import type SearchBook from '$lib/domain/SearchBook';

    export let books: Book[] | SearchBook[] = [];
    export let getBooks: ((page) => Promise<Book[] | SearchBook[]>) | undefined = undefined;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;
    export let isCurrentlyReading: boolean = false;

    const dispatch = createEventDispatcher<{
        editActivity: { book: Book; activity: ReadingActivity | null };
    }>();

    const activateScrollBeforePixels = 1000;
    let page = 1;
    let isScrollBeingHandled = false;

    async function onScroll() {
        if (getBooks === undefined || isScrollBeingHandled) {
            return;
        }

        isScrollBeingHandled = true;

        const {scrollTop, clientHeight, scrollHeight} = document.documentElement;
        if (scrollTop + clientHeight >= scrollHeight - activateScrollBeforePixels) {
            const nextPageBooks = await getBooks(page);
            books = [...books, ...nextPageBooks];
            page++;

            if (nextPageBooks.length === 0) {
                window.removeEventListener('scroll', onScroll);
            }
        }

        isScrollBeingHandled = false;
    }

    onMount(async () => {
        if (getBooks === undefined) {
            return;
        }

        books = await getBooks(page);
        page++;

        window.addEventListener('scroll', onScroll);
    });

    function handleEditActivity(event: CustomEvent<{ book: Book; activity: ReadingActivity | null }>) {
        dispatch('editActivity', event.detail);
    }

    onDestroy(() => {
        if (typeof window !== 'undefined') {
            window.removeEventListener('scroll', onScroll);
        }
    });
</script>

<div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
    {#each books as book, i (i)}
        {#if isCurrentlyReading}
            <BookListItemCurrentlyReading
                book={book}
                {deleteCallback}
                on:editActivity={handleEditActivity}
            />
        {:else}
            <BookListItem book={book} deleteCallback={deleteCallback} />
        {/if}
    {/each}
</div>
